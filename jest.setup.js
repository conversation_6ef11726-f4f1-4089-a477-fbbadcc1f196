import '@testing-library/jest-dom'

// Mock WebRTC APIs
global.RTCPeerConnection = jest.fn().mockImplementation(() => ({
  createOffer: jest.fn(),
  createAnswer: jest.fn(),
  setLocalDescription: jest.fn(),
  setRemoteDescription: jest.fn(),
  addIceCandidate: jest.fn(),
  addTrack: jest.fn(),
  close: jest.fn(),
  ontrack: null,
  onicecandidate: null,
  onconnectionstatechange: null,
  oniceconnectionstatechange: null,
  connectionState: 'new',
  iceConnectionState: 'new',
}))

global.navigator.mediaDevices = {
  getUserMedia: jest.fn().mockResolvedValue({
    getTracks: () => [
      { stop: jest.fn(), enabled: true },
      { stop: jest.fn(), enabled: true },
    ],
    getAudioTracks: () => [{ stop: jest.fn(), enabled: true }],
    getVideoTracks: () => [{ stop: jest.fn(), enabled: true }],
  }),
}

// Mock Socket.io
jest.mock('socket.io-client', () => ({
  io: jest.fn(() => ({
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn(),
    disconnect: jest.fn(),
  })),
}))

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;
