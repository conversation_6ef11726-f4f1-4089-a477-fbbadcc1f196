{"name": "next-ocr", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "socket": "node server.js", "dev:full": "concurrently \"npm run dev\" \"npm run socket\""}, "dependencies": {"@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.5", "@types/simple-peer": "^9.11.8", "@types/uuid": "^10.0.0", "lucide-react": "^0.525.0", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "simple-peer": "^9.11.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^9.2.0", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}