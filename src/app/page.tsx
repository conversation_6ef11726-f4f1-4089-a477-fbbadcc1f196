'use client';

import React, { useState, useEffect } from 'react';
import { AuthForm } from '../components/AuthForm';
import { RoomList } from '../components/RoomList';
import { CreateRoomModal } from '../components/CreateRoomModal';
import { ChatInterface } from '../components/ChatInterface';
import { authService } from '../lib/auth';
import { useRoomManagement } from '../hooks/useRoomManagement';
import type { User, Room } from '../types';

export default function Home() {
  const [user, setUser] = useState<User | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [loading, setLoading] = useState(true);

  const {
    rooms,
    currentRoom,
    loading: roomLoading,
    error: roomError,
    createRoom,
    joinRoom,
    leaveRoom,
  } = useRoomManagement({ user });

  useEffect(() => {
    // Check if user is already authenticated
    const checkAuth = async () => {
      try {
        const currentUser = await authService.getCurrentUser();
        setUser(currentUser);
      } catch (error) {
        console.error('Error checking auth:', error);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();

    // Listen for auth state changes
    const { data: { subscription } } = authService.onAuthStateChange((user) => {
      setUser(user);
    });

    return () => {
      subscription?.unsubscribe();
    };
  }, []);

  const handleSignOut = async () => {
    try {
      await authService.signOut();
      setUser(null);
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const handleRoomSelect = async (room: Room) => {
    try {
      await joinRoom(room);
    } catch (error) {
      console.error('Error joining room:', error);
    }
  };

  const handleLeaveRoom = async () => {
    try {
      await leaveRoom();
    } catch (error) {
      console.error('Error leaving room:', error);
    }
  };

  const handleRoomCreated = async (roomData: {
    name: string;
    description?: string;
    isPrivate?: boolean;
    maxParticipants?: number;
  }) => {
    try {
      const room = await createRoom(roomData);
      await joinRoom(room);
      setShowCreateModal(false);
    } catch (error) {
      console.error('Error creating room:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (!user) {
    return <AuthForm onSuccess={() => {}} />;
  }

  if (currentRoom) {
    return (
      <ChatInterface
        room={currentRoom}
        user={user}
        onLeaveRoom={handleLeaveRoom}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Realtime Chat & Video
              </h1>
              <p className="text-gray-600">Welcome, {user.username}!</p>
            </div>
            <button
              onClick={handleSignOut}
              className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              Sign Out
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <RoomList
            user={user}
            onRoomSelect={handleRoomSelect}
            onCreateRoom={() => setShowCreateModal(true)}
          />
        </div>
      </div>

      <CreateRoomModal
        user={user}
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onRoomCreated={handleRoomCreated}
      />
    </div>
  );
}
