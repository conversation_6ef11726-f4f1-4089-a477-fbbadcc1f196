'use client';

import React, { useState, useEffect } from 'react';
import { AuthForm } from '../components/AuthForm';
import { RoomList } from '../components/RoomList';
import { CreateRoomModal } from '../components/CreateRoomModal';
import { ChatInterface } from '../components/ChatInterface';
import { authService } from '../lib/auth';
import { useRoomManagement } from '../hooks/useRoomManagement';
import { useSocket } from '../contexts/SocketContext';
import type { User, Room } from '../types';

export default function Home() {
  const [user, setUser] = useState<User | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showUserSetup, setShowUserSetup] = useState(true);
  const [username, setUsername] = useState('');
  const [loading, setLoading] = useState(false);

  const { socket, identifyUser } = useSocket();

  const {
    rooms,
    currentRoom,
    loading: roomLoading,
    error: roomError,
    createRoom,
    joinRoom,
    leaveRoom,
  } = useRoomManagement({ user });

  // Create a simple user without authentication
  const handleUserSetup = (e: React.FormEvent) => {
    e.preventDefault();
    if (!username.trim()) return;

    const newUser: User = {
      id: `user-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`,
      email: `${username}@example.com`,
      username: username.trim(),
      created_at: new Date().toISOString(),
    };

    setUser(newUser);
    setShowUserSetup(false);

    // Identify user with socket server
    if (socket) {
      identifyUser(newUser.id);
    }
  };

  // Identify user when socket connects
  useEffect(() => {
    if (socket && user) {
      identifyUser(user.id);
    }
  }, [socket, user, identifyUser]);

  const handleSignOut = () => {
    setUser(null);
    setShowUserSetup(true);
    setUsername('');
  };

  const handleRoomSelect = async (room: Room) => {
    try {
      await joinRoom(room);
    } catch (error) {
      console.error('Error joining room:', error);
    }
  };

  const handleLeaveRoom = async () => {
    try {
      await leaveRoom();
    } catch (error) {
      console.error('Error leaving room:', error);
    }
  };

  const handleRoomCreated = async (roomData: {
    name: string;
    description?: string;
    isPrivate?: boolean;
    maxParticipants?: number;
  }) => {
    try {
      const room = await createRoom(roomData);
      await joinRoom(room);
      setShowCreateModal(false);
    } catch (error) {
      console.error('Error creating room:', error);
    }
  };

  if (showUserSetup) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8 p-8">
          <div className="text-center">
            <h2 className="text-3xl font-extrabold text-gray-900 mb-2">
              Join the Chat
            </h2>
            <p className="text-gray-600">Enter your username to get started</p>
          </div>

          <form onSubmit={handleUserSetup} className="space-y-4">
            <div>
              <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-2">
                Username
              </label>
              <input
                id="username"
                type="text"
                required
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder="Enter your username"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-gray-900 placeholder-gray-500"
                maxLength={20}
              />
            </div>

            <button
              type="submit"
              disabled={!username.trim() || loading}
              className="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
            >
              {loading ? 'Joining...' : 'Join Chat'}
            </button>
          </form>
        </div>
      </div>
    );
  }

  if (currentRoom) {
    return (
      <ChatInterface
        room={currentRoom}
        user={user}
        onLeaveRoom={handleLeaveRoom}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Realtime Chat & Video
              </h1>
              <p className="text-gray-600">Welcome, {user?.username}!</p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-500">
                User ID: {user?.id.slice(-8)}
              </div>
              <button
                onClick={handleSignOut}
                className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
              >
                Change User
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <RoomList
            user={user}
            rooms={rooms}
            loading={roomLoading}
            onRoomSelect={handleRoomSelect}
            onCreateRoom={() => setShowCreateModal(true)}
          />
        </div>
      </div>

      <CreateRoomModal
        user={user}
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onRoomCreated={handleRoomCreated}
      />
    </div>
  );
}
