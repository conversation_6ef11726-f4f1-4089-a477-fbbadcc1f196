import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '../../../lib/supabase';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerClient();
    
    const { data: rooms, error } = await supabase
      .from('rooms')
      .select(`
        *,
        participants:room_participants(
          *,
          user:users(*)
        )
      `)
      .eq('is_private', false)
      .order('created_at', { ascending: false });

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    return NextResponse.json({ rooms });
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerClient();
    const body = await request.json();
    
    const { name, description, isPrivate, maxParticipants, hostId } = body;

    // Create the room
    const { data: room, error: roomError } = await supabase
      .from('rooms')
      .insert({
        name,
        description,
        host_id: hostId,
        is_private: isPrivate || false,
        max_participants: maxParticipants || 10,
      })
      .select()
      .single();

    if (roomError) {
      return NextResponse.json({ error: roomError.message }, { status: 400 });
    }

    // Add the host as a participant
    const { error: participantError } = await supabase
      .from('room_participants')
      .insert({
        user_id: hostId,
        room_id: room.id,
        is_host: true,
      });

    if (participantError) {
      return NextResponse.json({ error: participantError.message }, { status: 400 });
    }

    // Fetch the complete room data with participants
    const { data: completeRoom } = await supabase
      .from('rooms')
      .select(`
        *,
        participants:room_participants(
          *,
          user:users(*)
        )
      `)
      .eq('id', room.id)
      .single();

    return NextResponse.json({ room: completeRoom });
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
