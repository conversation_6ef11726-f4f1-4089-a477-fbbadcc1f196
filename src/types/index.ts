export interface User {
  id: string;
  email: string;
  username: string;
  avatar_url?: string;
  created_at: string;
}

export interface Room {
  id: string;
  name: string;
  description?: string;
  host_id: string;
  is_private: boolean;
  max_participants: number;
  created_at: string;
  participants: RoomParticipant[];
}

export interface RoomParticipant {
  user_id: string;
  room_id: string;
  joined_at: string;
  is_host: boolean;
  user: User;
}

export interface Message {
  id: string;
  room_id: string;
  user_id: string;
  content: string;
  message_type: 'text' | 'system' | 'video_call_start' | 'video_call_end';
  created_at: string;
  user: User;
}

export interface VideoCall {
  id: string;
  room_id: string;
  initiated_by: string;
  participants: string[];
  status: 'active' | 'ended';
  started_at: string;
  ended_at?: string;
}

export interface SocketEvents {
  // User identification
  'identify-user': (userId: string) => void;

  // Room events
  'join-room': (roomId: string, userId: string) => void;
  'leave-room': (roomId: string, userId: string) => void;
  'room-joined': (room: Room) => void;
  'room-left': (roomId: string) => void;
  'user-joined-room': (user: User, roomId: string) => void;
  'user-left-room': (userId: string, roomId: string) => void;
  'room-participants-updated': (data: { roomId: string; participantCount: number; participants: string[] }) => void;

  // Chat events
  'send-message': (message: Omit<Message, 'id' | 'created_at'>) => void;
  'new-message': (message: Message) => void;
  'typing-start': (userId: string, roomId: string) => void;
  'typing-stop': (userId: string, roomId: string) => void;
  'user-typing': (userId: string, roomId: string) => void;
  'user-stopped-typing': (userId: string, roomId: string) => void;

  // Video call events
  'initiate-video-call': (roomId: string, userId: string) => void;
  'video-call-initiated': (callId: string, initiatorId: string, roomId: string) => void;
  'join-video-call': (callId: string, userId: string) => void;
  'leave-video-call': (callId: string, userId: string) => void;
  'video-call-joined': (callId: string, userId: string) => void;
  'video-call-left': (callId: string, userId: string) => void;
  'video-call-ended': (callId: string) => void;

  // WebRTC signaling
  'webrtc-offer': (offer: RTCSessionDescriptionInit, targetUserId: string, fromUserId: string) => void;
  'webrtc-answer': (answer: RTCSessionDescriptionInit, targetUserId: string, fromUserId: string) => void;
  'webrtc-ice-candidate': (candidate: RTCIceCandidateInit, targetUserId: string, fromUserId: string) => void;
  'webrtc-signal': (signal: any, targetUserId: string, fromUserId: string) => void;
}

export interface AppState {
  user: User | null;
  currentRoom: Room | null;
  rooms: Room[];
  messages: Message[];
  typingUsers: string[];
  isConnected: boolean;
  activeVideoCall: VideoCall | null;
}
