'use client';

import { useEffect, useRef, useState } from 'react';
import { useSocket } from '../contexts/SocketContext';

interface UseWebRTCProps {
  userId: string;
  roomId: string;
}

interface PeerConnection {
  userId: string;
  connection: RTCPeerConnection;
  stream?: MediaStream;
}

export const useWebRTC = ({ userId, roomId }: UseWebRTCProps) => {
  const { socket } = useSocket();
  const [localStream, setLocalStream] = useState<MediaStream | null>(null);
  const [remoteStreams, setRemoteStreams] = useState<Map<string, MediaStream>>(new Map());
  const [isCallActive, setIsCallActive] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoOff, setIsVideoOff] = useState(false);
  const [callError, setCallError] = useState<string | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);

  const peerConnections = useRef<Map<string, RTCPeerConnection>>(new Map());
  const localVideoRef = useRef<HTMLVideoElement>(null);
  const currentCallId = useRef<string | null>(null);

  const rtcConfig: RTCConfiguration = {
    iceServers: [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' },
    ],
  };

  const startCall = async () => {
    setIsConnecting(true);
    setCallError(null);

    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true,
      });

      setLocalStream(stream);
      setIsCallActive(true);

      // Set the video source immediately
      if (localVideoRef.current) {
        localVideoRef.current.srcObject = stream;
        // Force play the video
        setTimeout(() => {
          if (localVideoRef.current) {
            localVideoRef.current.play().catch(console.error);
          }
        }, 100);
      }

      // Notify other users in the room about the call
      socket?.emit('initiate-video-call', roomId, userId);

      console.log('Video call started successfully, local stream:', stream);
      console.log('Video tracks:', stream.getVideoTracks());
      console.log('Audio tracks:', stream.getAudioTracks());
    } catch (error: any) {
      console.error('Error starting call:', error);
      setCallError(error.message || 'Failed to start video call');
      setIsCallActive(false);
    } finally {
      setIsConnecting(false);
    }
  };

  const endCall = () => {
    // Stop local stream
    if (localStream) {
      localStream.getTracks().forEach(track => track.stop());
      setLocalStream(null);
    }

    // Close all peer connections
    peerConnections.current.forEach(pc => pc.close());
    peerConnections.current.clear();

    // Clear remote streams
    setRemoteStreams(new Map());
    setIsCallActive(false);
    setCallError(null);
    setIsConnecting(false);

    // Notify server if we have an active call
    if (currentCallId.current) {
      socket?.emit('leave-video-call', currentCallId.current, userId);
      currentCallId.current = null;
    }
  };

  const toggleMute = () => {
    if (localStream) {
      const audioTrack = localStream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        setIsMuted(!audioTrack.enabled);
        console.log('Audio toggled:', audioTrack.enabled ? 'unmuted' : 'muted');
      }
    }
  };

  const toggleVideo = () => {
    if (localStream) {
      const videoTrack = localStream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled;
        setIsVideoOff(!videoTrack.enabled);
        console.log('Video toggled:', videoTrack.enabled ? 'on' : 'off');
      }
    }
  };

  const createPeerConnection = async (targetUserId: string): Promise<RTCPeerConnection> => {
    const pc = new RTCPeerConnection(rtcConfig);

    // Add local stream to peer connection
    if (localStream) {
      localStream.getTracks().forEach(track => {
        console.log(`Adding track to peer connection for ${targetUserId}:`, track.kind);
        pc.addTrack(track, localStream);
      });
    }

    // Handle remote stream
    pc.ontrack = (event) => {
      console.log(`Received remote track from ${targetUserId}:`, event.track.kind);
      const [remoteStream] = event.streams;
      console.log('Remote stream:', remoteStream);
      setRemoteStreams(prev => {
        const newMap = new Map(prev.set(targetUserId, remoteStream));
        console.log('Updated remote streams:', newMap);
        return newMap;
      });
    };

    // Handle ICE candidates
    pc.onicecandidate = (event) => {
      if (event.candidate) {
        socket?.emit('webrtc-ice-candidate', event.candidate, targetUserId, userId);
      }
    };

    // Handle connection state changes
    pc.onconnectionstatechange = () => {
      console.log(`Connection state with ${targetUserId}:`, pc.connectionState);

      if (pc.connectionState === 'failed' || pc.connectionState === 'disconnected') {
        // Remove the peer connection and remote stream
        peerConnections.current.delete(targetUserId);
        setRemoteStreams(prev => {
          const newMap = new Map(prev);
          newMap.delete(targetUserId);
          return newMap;
        });
      }
    };

    // Handle ICE connection state changes
    pc.oniceconnectionstatechange = () => {
      console.log(`ICE connection state with ${targetUserId}:`, pc.iceConnectionState);
    };

    peerConnections.current.set(targetUserId, pc);
    return pc;
  };

  const handleOffer = async (offer: RTCSessionDescriptionInit, fromUserId: string) => {
    try {
      const pc = await createPeerConnection(fromUserId);
      await pc.setRemoteDescription(offer);
      
      const answer = await pc.createAnswer();
      await pc.setLocalDescription(answer);
      
      socket?.emit('webrtc-answer', answer, fromUserId, userId);
    } catch (error) {
      console.error('Error handling offer:', error);
    }
  };

  const handleAnswer = async (answer: RTCSessionDescriptionInit, fromUserId: string) => {
    try {
      const pc = peerConnections.current.get(fromUserId);
      if (pc) {
        await pc.setRemoteDescription(answer);
      }
    } catch (error) {
      console.error('Error handling answer:', error);
    }
  };

  const handleIceCandidate = async (candidate: RTCIceCandidateInit, fromUserId: string) => {
    try {
      const pc = peerConnections.current.get(fromUserId);
      if (pc) {
        await pc.addIceCandidate(candidate);
      }
    } catch (error) {
      console.error('Error handling ICE candidate:', error);
    }
  };

  const initiateCallWithUser = async (targetUserId: string) => {
    try {
      const pc = await createPeerConnection(targetUserId);
      const offer = await pc.createOffer();
      await pc.setLocalDescription(offer);
      
      socket?.emit('webrtc-offer', offer, targetUserId, userId);
    } catch (error) {
      console.error('Error initiating call:', error);
    }
  };

  useEffect(() => {
    if (!socket) return;

    // Video call initiation is now handled in ChatInterface

    socket.on('video-call-joined', (callId, joinedUserId) => {
      if (joinedUserId !== userId && localStream) {
        initiateCallWithUser(joinedUserId);
      }
    });

    socket.on('webrtc-offer', handleOffer);
    socket.on('webrtc-answer', handleAnswer);
    socket.on('webrtc-ice-candidate', handleIceCandidate);

    socket.on('video-call-left', (callId, leftUserId) => {
      const pc = peerConnections.current.get(leftUserId);
      if (pc) {
        pc.close();
        peerConnections.current.delete(leftUserId);
      }
      setRemoteStreams(prev => {
        const newMap = new Map(prev);
        newMap.delete(leftUserId);
        return newMap;
      });
    });

    socket.on('video-call-ended', () => {
      endCall();
    });

    return () => {
      socket.off('video-call-joined');
      socket.off('webrtc-offer');
      socket.off('webrtc-answer');
      socket.off('webrtc-ice-candidate');
      socket.off('video-call-left');
      socket.off('video-call-ended');
    };
  }, [socket, userId, localStream]);

  return {
    localStream,
    remoteStreams,
    isCallActive,
    isMuted,
    isVideoOff,
    callError,
    isConnecting,
    localVideoRef,
    startCall,
    endCall,
    toggleMute,
    toggleVideo,
    setCallError,
  };
};
