'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSocket } from '../contexts/SocketContext';
import { roomService } from '../lib/roomService';
import type { Room, User } from '../types';

interface UseRoomManagementProps {
  user: User | null;
}

export const useRoomManagement = ({ user }: UseRoomManagementProps) => {
  const { socket, isConnected } = useSocket();
  const [rooms, setRooms] = useState<Room[]>([]);
  const [currentRoom, setCurrentRoom] = useState<Room | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch available rooms
  const fetchRooms = useCallback(async () => {
    if (!user) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const fetchedRooms = await roomService.getRooms();
      setRooms(fetchedRooms);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Create a new room
  const createRoom = useCallback(async (roomData: {
    name: string;
    description?: string;
    isPrivate?: boolean;
    maxParticipants?: number;
  }) => {
    if (!user) throw new Error('User not authenticated');

    setLoading(true);
    setError(null);

    try {
      const newRoom = await roomService.createRoom({
        ...roomData,
        hostId: user.id,
      });
      
      setRooms(prev => [newRoom, ...prev]);
      return newRoom;
    } catch (err: any) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Join a room
  const joinRoom = useCallback(async (room: Room) => {
    if (!user || !socket) return;

    setLoading(true);
    setError(null);

    try {
      // Check if user is already in the room
      const isAlreadyInRoom = await roomService.isUserInRoom(room.id, user.id);
      
      if (!isAlreadyInRoom) {
        await roomService.joinRoom(room.id, user.id);
      }

      // Emit socket event to join room
      socket.emit('join-room', room.id, user.id);
      
      setCurrentRoom(room);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [user, socket]);

  // Leave current room
  const leaveRoom = useCallback(async () => {
    if (!user || !socket || !currentRoom) return;

    setLoading(true);
    setError(null);

    try {
      // Emit socket event to leave room
      socket.emit('leave-room', currentRoom.id, user.id);
      
      // Remove from database
      await roomService.leaveRoom(currentRoom.id, user.id);
      
      setCurrentRoom(null);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [user, socket, currentRoom]);

  // Delete a room (only for hosts)
  const deleteRoom = useCallback(async (roomId: string) => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      await roomService.deleteRoom(roomId);
      setRooms(prev => prev.filter(room => room.id !== roomId));
      
      if (currentRoom?.id === roomId) {
        setCurrentRoom(null);
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [user, currentRoom]);

  // Update room details
  const updateRoom = useCallback(async (roomId: string, updates: Partial<Room>) => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      const updatedRoom = await roomService.updateRoom(roomId, updates);
      
      setRooms(prev => prev.map(room => 
        room.id === roomId ? updatedRoom : room
      ));
      
      if (currentRoom?.id === roomId) {
        setCurrentRoom(updatedRoom);
      }
      
      return updatedRoom;
    } catch (err: any) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [user, currentRoom]);

  // Socket event handlers
  useEffect(() => {
    if (!socket || !user) return;

    const handleRoomJoined = (room: Room) => {
      setCurrentRoom(room);
    };

    const handleRoomLeft = (roomId: string) => {
      if (currentRoom?.id === roomId) {
        setCurrentRoom(null);
      }
    };

    const handleUserJoinedRoom = (userId: string, roomId: string) => {
      if (currentRoom?.id === roomId) {
        // Refresh current room data to get updated participants
        roomService.getRoom(roomId).then(updatedRoom => {
          setCurrentRoom(updatedRoom);
        }).catch(console.error);
      }
    };

    const handleUserLeftRoom = (userId: string, roomId: string) => {
      if (currentRoom?.id === roomId) {
        // Refresh current room data to get updated participants
        roomService.getRoom(roomId).then(updatedRoom => {
          setCurrentRoom(updatedRoom);
        }).catch(console.error);
      }
    };

    socket.on('room-joined', handleRoomJoined);
    socket.on('room-left', handleRoomLeft);
    socket.on('user-joined-room', handleUserJoinedRoom);
    socket.on('user-left-room', handleUserLeftRoom);

    return () => {
      socket.off('room-joined', handleRoomJoined);
      socket.off('room-left', handleRoomLeft);
      socket.off('user-joined-room', handleUserJoinedRoom);
      socket.off('user-left-room', handleUserLeftRoom);
    };
  }, [socket, user, currentRoom]);

  // Initial data fetch
  useEffect(() => {
    if (user && isConnected) {
      fetchRooms();
    }
  }, [user, isConnected, fetchRooms]);

  return {
    rooms,
    currentRoom,
    loading,
    error,
    createRoom,
    joinRoom,
    leaveRoom,
    deleteRoom,
    updateRoom,
    fetchRooms,
    setError,
  };
};
