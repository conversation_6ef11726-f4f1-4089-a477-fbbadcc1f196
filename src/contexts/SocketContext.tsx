'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { io, Socket } from 'socket.io-client';
import type { SocketEvents } from '../types';

interface SocketContextType {
  socket: Socket<SocketEvents> | null;
  isConnected: boolean;
  identifyUser: (userId: string) => void;
}

const SocketContext = createContext<SocketContextType>({
  socket: null,
  isConnected: false,
  identifyUser: () => {},
});

export const useSocket = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

interface SocketProviderProps {
  children: React.ReactNode;
}

export const SocketProvider: React.FC<SocketProviderProps> = ({ children }) => {
  const [socket, setSocket] = useState<Socket<SocketEvents> | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  const identifyUser = (userId: string) => {
    if (socket) {
      socket.emit('identify-user', userId);
    }
  };

  useEffect(() => {
    const socketInstance = io(process.env.NODE_ENV === 'production'
      ? window.location.origin
      : 'http://localhost:3001',
      {
        withCredentials: true,
        transports: ['websocket', 'polling']
      }
    );

    socketInstance.on('connect', () => {
      console.log('Connected to socket server');
      setIsConnected(true);
    });

    socketInstance.on('disconnect', () => {
      console.log('Disconnected from socket server');
      setIsConnected(false);
    });

    setSocket(socketInstance);

    return () => {
      socketInstance.disconnect();
    };
  }, []);

  return (
    <SocketContext.Provider value={{ socket, isConnected, identifyUser }}>
      {children}
    </SocketContext.Provider>
  );
};
