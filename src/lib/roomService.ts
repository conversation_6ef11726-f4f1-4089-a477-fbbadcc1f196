import { supabase } from './supabase';
import type { Room, User } from '../types';

export const roomService = {
  async createRoom(data: {
    name: string;
    description?: string;
    isPrivate?: boolean;
    maxParticipants?: number;
    hostId: string;
  }): Promise<Room> {
    const response = await fetch('/api/rooms', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    const result = await response.json();
    if (!response.ok) {
      throw new Error(result.error || 'Failed to create room');
    }

    return result.room;
  },

  async getRooms(): Promise<Room[]> {
    const response = await fetch('/api/rooms');
    const result = await response.json();
    
    if (!response.ok) {
      throw new Error(result.error || 'Failed to fetch rooms');
    }

    return result.rooms || [];
  },

  async getRoom(roomId: string): Promise<Room> {
    const response = await fetch(`/api/rooms/${roomId}`);
    const result = await response.json();
    
    if (!response.ok) {
      throw new Error(result.error || 'Failed to fetch room');
    }

    return result.room;
  },

  async updateRoom(roomId: string, updates: Partial<Room>): Promise<Room> {
    const response = await fetch(`/api/rooms/${roomId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    });

    const result = await response.json();
    if (!response.ok) {
      throw new Error(result.error || 'Failed to update room');
    }

    return result.room;
  },

  async deleteRoom(roomId: string): Promise<void> {
    const response = await fetch(`/api/rooms/${roomId}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const result = await response.json();
      throw new Error(result.error || 'Failed to delete room');
    }
  },

  async joinRoom(roomId: string, userId: string): Promise<void> {
    const { error } = await supabase
      .from('room_participants')
      .upsert({ 
        user_id: userId, 
        room_id: roomId, 
        is_host: false 
      });

    if (error) {
      throw new Error(error.message);
    }
  },

  async leaveRoom(roomId: string, userId: string): Promise<void> {
    const { error } = await supabase
      .from('room_participants')
      .delete()
      .eq('user_id', userId)
      .eq('room_id', roomId);

    if (error) {
      throw new Error(error.message);
    }
  },

  async getRoomParticipants(roomId: string): Promise<User[]> {
    const { data, error } = await supabase
      .from('room_participants')
      .select(`
        user:users(*)
      `)
      .eq('room_id', roomId);

    if (error) {
      throw new Error(error.message);
    }

    return data?.map(item => item.user).filter(Boolean) || [];
  },

  async isUserInRoom(roomId: string, userId: string): Promise<boolean> {
    const { data, error } = await supabase
      .from('room_participants')
      .select('user_id')
      .eq('room_id', roomId)
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
      throw new Error(error.message);
    }

    return !!data;
  },

  async getUserRooms(userId: string): Promise<Room[]> {
    const { data, error } = await supabase
      .from('room_participants')
      .select(`
        room:rooms(
          *,
          participants:room_participants(
            *,
            user:users(*)
          )
        )
      `)
      .eq('user_id', userId);

    if (error) {
      throw new Error(error.message);
    }

    return data?.map(item => item.room).filter(Boolean) || [];
  },

  // Real-time subscriptions
  subscribeToRoomChanges(callback: (payload: any) => void) {
    return supabase
      .channel('rooms')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'rooms' }, 
        callback
      )
      .subscribe();
  },

  subscribeToRoomParticipants(roomId: string, callback: (payload: any) => void) {
    return supabase
      .channel(`room_participants:${roomId}`)
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: 'room_participants',
          filter: `room_id=eq.${roomId}`
        }, 
        callback
      )
      .subscribe();
  },
};
