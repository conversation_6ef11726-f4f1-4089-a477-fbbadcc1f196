import { mockRooms, addRoom, addParticipant, removeParticipant, getRoomParticipants } from './mockData';
import type { Room, User } from '../types';

export const roomService = {
  async createRoom(data: {
    name: string;
    description?: string;
    isPrivate?: boolean;
    maxParticipants?: number;
    hostId: string;
  }): Promise<Room> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const newRoom = addRoom({
      name: data.name,
      description: data.description,
      host_id: data.hostId,
      is_private: data.isPrivate || false,
      max_participants: data.maxParticipants || 10,
    });

    return newRoom;
  },

  async getRooms(): Promise<Room[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    return [...mockRooms];
  },

  async getRoom(roomId: string): Promise<Room> {
    await new Promise(resolve => setTimeout(resolve, 200));

    const room = mockRooms.find(r => r.id === roomId);
    if (!room) {
      throw new Error('Room not found');
    }

    return room;
  },

  async updateRoom(roomId: string, updates: Partial<Room>): Promise<Room> {
    await new Promise(resolve => setTimeout(resolve, 300));

    const roomIndex = mockRooms.findIndex(r => r.id === roomId);
    if (roomIndex === -1) {
      throw new Error('Room not found');
    }

    mockRooms[roomIndex] = { ...mockRooms[roomIndex], ...updates };
    return mockRooms[roomIndex];
  },

  async deleteRoom(roomId: string): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 300));

    const roomIndex = mockRooms.findIndex(r => r.id === roomId);
    if (roomIndex === -1) {
      throw new Error('Room not found');
    }

    mockRooms.splice(roomIndex, 1);
  },

  async joinRoom(roomId: string, user: User): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 200));

    addParticipant(roomId, user);
  },

  async leaveRoom(roomId: string, userId: string): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 200));

    removeParticipant(roomId, userId);
  },

  async getRoomParticipants(roomId: string): Promise<User[]> {
    await new Promise(resolve => setTimeout(resolve, 100));

    const participants = getRoomParticipants(roomId);
    return participants.map(p => p.user);
  },

  async isUserInRoom(roomId: string, userId: string): Promise<boolean> {
    const participants = getRoomParticipants(roomId);
    return participants.some(p => p.user_id === userId);
  },

  async getUserRooms(userId: string): Promise<Room[]> {
    await new Promise(resolve => setTimeout(resolve, 200));

    const userParticipations = getRoomParticipants('').filter(p => p.user_id === userId);
    const userRoomIds = userParticipations.map(p => p.room_id);

    return mockRooms.filter(room => userRoomIds.includes(room.id));
  },

  // Mock real-time subscriptions (no-op for testing)
  subscribeToRoomChanges(callback: (payload: any) => void) {
    return {
      unsubscribe: () => {}
    };
  },

  subscribeToRoomParticipants(roomId: string, callback: (payload: any) => void) {
    return {
      unsubscribe: () => {}
    };
  },
};
