import type { User } from '../types';

// Mock user storage (in a real app, this would be in a database)
const mockUsers: User[] = [];
let currentUser: User | null = null;

// Generate a simple ID
const generateId = () => Math.random().toString(36).substr(2, 9);

export const authService = {
  async signUp(email: string, password: string, username: string) {
    // Check if user already exists
    const existingUser = mockUsers.find(u => u.email === email || u.username === username);
    if (existingUser) {
      throw new Error('User already exists');
    }

    // Create new user
    const newUser: User = {
      id: generateId(),
      email,
      username,
      created_at: new Date().toISOString(),
    };

    mockUsers.push(newUser);
    currentUser = newUser;

    // Store in localStorage for persistence
    localStorage.setItem('currentUser', JSON.stringify(newUser));

    return { user: newUser };
  },

  async signIn(email: string, password: string) {
    // Find user by email
    const user = mockUsers.find(u => u.email === email);
    if (!user) {
      throw new Error('User not found');
    }

    currentUser = user;
    localStorage.setItem('currentUser', JSON.stringify(user));

    return { user };
  },

  async signOut() {
    currentUser = null;
    localStorage.removeItem('currentUser');
  },

  async getCurrentUser(): Promise<User | null> {
    if (currentUser) return currentUser;

    // Try to restore from localStorage
    const stored = localStorage.getItem('currentUser');
    if (stored) {
      currentUser = JSON.parse(stored);
      // Make sure this user exists in our mock database
      if (!mockUsers.find(u => u.id === currentUser?.id)) {
        mockUsers.push(currentUser!);
      }
    }

    return currentUser;
  },

  async updateProfile(userId: string, updates: Partial<User>) {
    const userIndex = mockUsers.findIndex(u => u.id === userId);
    if (userIndex === -1) {
      throw new Error('User not found');
    }

    const updatedUser = { ...mockUsers[userIndex], ...updates };
    mockUsers[userIndex] = updatedUser;

    if (currentUser?.id === userId) {
      currentUser = updatedUser;
      localStorage.setItem('currentUser', JSON.stringify(updatedUser));
    }

    return updatedUser;
  },

  onAuthStateChange(callback: (user: User | null) => void) {
    // Simple implementation - just call immediately with current user
    callback(currentUser);

    // Return a mock subscription object
    return {
      data: {
        subscription: {
          unsubscribe: () => {}
        }
      }
    };
  },
};
