import type { Room, Message, User, RoomParticipant } from '../types';

// Mock data storage
export const mockRooms: Room[] = [
  {
    id: 'room-1',
    name: 'General <PERSON><PERSON>',
    description: 'A place for general discussions',
    host_id: 'user-1',
    is_private: false,
    max_participants: 10,
    created_at: new Date().toISOString(),
    participants: []
  },
  {
    id: 'room-2',
    name: 'Tech Talk',
    description: 'Discuss the latest in technology',
    host_id: 'user-1',
    is_private: false,
    max_participants: 15,
    created_at: new Date().toISOString(),
    participants: []
  }
];

export const mockMessages: Message[] = [
  {
    id: 'msg-1',
    room_id: 'room-1',
    user_id: 'user-1',
    content: 'Welcome to the general chat!',
    message_type: 'text',
    created_at: new Date(Date.now() - 60000).toISOString(),
    user: {
      id: 'user-1',
      email: '<EMAIL>',
      username: 'Demo User',
      created_at: new Date().toISOString()
    }
  }
];

export const mockParticipants: RoomParticipant[] = [];

// Helper functions
export const generateId = () => Math.random().toString(36).substr(2, 9);

export const addRoom = (room: Omit<Room, 'id' | 'created_at'>) => {
  const newRoom: Room = {
    ...room,
    id: generateId(),
    created_at: new Date().toISOString(),
    participants: []
  };
  mockRooms.push(newRoom);
  return newRoom;
};

export const addMessage = (message: Omit<Message, 'id' | 'created_at'>) => {
  const newMessage: Message = {
    ...message,
    id: generateId(),
    created_at: new Date().toISOString()
  };
  mockMessages.push(newMessage);
  return newMessage;
};

export const addParticipant = (roomId: string, user: User) => {
  const participant: RoomParticipant = {
    user_id: user.id,
    room_id: roomId,
    joined_at: new Date().toISOString(),
    is_host: false,
    user
  };
  
  mockParticipants.push(participant);
  
  // Update room participants
  const room = mockRooms.find(r => r.id === roomId);
  if (room) {
    room.participants = mockParticipants.filter(p => p.room_id === roomId);
  }
  
  return participant;
};

export const removeParticipant = (roomId: string, userId: string) => {
  const index = mockParticipants.findIndex(p => p.room_id === roomId && p.user_id === userId);
  if (index > -1) {
    mockParticipants.splice(index, 1);
  }
  
  // Update room participants
  const room = mockRooms.find(r => r.id === roomId);
  if (room) {
    room.participants = mockParticipants.filter(p => p.room_id === roomId);
  }
};

export const getRoomMessages = (roomId: string, limit = 50) => {
  return mockMessages
    .filter(m => m.room_id === roomId)
    .sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
    .slice(-limit);
};

export const getRoomParticipants = (roomId: string) => {
  return mockParticipants.filter(p => p.room_id === roomId);
};
