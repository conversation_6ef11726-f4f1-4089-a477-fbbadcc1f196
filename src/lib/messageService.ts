import { mockMessages, addMessage, getRoomMessages } from './mockData';
import type { Message } from '../types';

export const messageService = {
  async getMessages(roomId: string, limit = 50, offset = 0): Promise<Message[]> {
    await new Promise(resolve => setTimeout(resolve, 200));

    return getRoomMessages(roomId, limit);
  },

  async sendMessage(data: {
    roomId: string;
    userId: string;
    content: string;
    messageType?: string;
  }): Promise<Message> {
    await new Promise(resolve => setTimeout(resolve, 100));

    // For mock purposes, we'll create a simple user object
    const mockUser = {
      id: data.userId,
      email: '<EMAIL>',
      username: `User ${data.userId.slice(-4)}`,
      created_at: new Date().toISOString()
    };

    const newMessage = addMessage({
      room_id: data.roomId,
      user_id: data.userId,
      content: data.content,
      message_type: data.messageType || 'text',
      user: mockUser
    });

    return newMessage;
  },

  async deleteMessage(messageId: string): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 200));

    const messageIndex = mockMessages.findIndex(m => m.id === messageId);
    if (messageIndex > -1) {
      mockMessages.splice(messageIndex, 1);
    }
  },

  // Mock real-time subscriptions (no-op for testing)
  subscribeToMessages(roomId: string, callback: (payload: any) => void) {
    return {
      unsubscribe: () => {}
    };
  },
};
