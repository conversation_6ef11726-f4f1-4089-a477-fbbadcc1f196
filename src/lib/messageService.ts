import { supabase } from './supabase';
import type { Message } from '../types';

export const messageService = {
  async getMessages(roomId: string, limit = 50, offset = 0): Promise<Message[]> {
    const response = await fetch(
      `/api/messages?roomId=${roomId}&limit=${limit}&offset=${offset}`
    );
    const result = await response.json();
    
    if (!response.ok) {
      throw new Error(result.error || 'Failed to fetch messages');
    }

    return result.messages || [];
  },

  async sendMessage(data: {
    roomId: string;
    userId: string;
    content: string;
    messageType?: string;
  }): Promise<Message> {
    const response = await fetch('/api/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    const result = await response.json();
    if (!response.ok) {
      throw new Error(result.error || 'Failed to send message');
    }

    return result.message;
  },

  async deleteMessage(messageId: string): Promise<void> {
    const { error } = await supabase
      .from('messages')
      .delete()
      .eq('id', messageId);

    if (error) {
      throw new Error(error.message);
    }
  },

  // Real-time subscriptions
  subscribeToMessages(roomId: string, callback: (payload: any) => void) {
    return supabase
      .channel(`messages:${roomId}`)
      .on('postgres_changes', 
        { 
          event: 'INSERT', 
          schema: 'public', 
          table: 'messages',
          filter: `room_id=eq.${roomId}`
        }, 
        callback
      )
      .subscribe();
  },
};
