'use client';

import React, { useState, useEffect } from 'react';
import { Plus, Users, Lock } from 'lucide-react';
import type { Room, User } from '../types';

interface RoomListProps {
  user: User;
  rooms: Room[];
  loading: boolean;
  onRoomSelect: (room: Room) => void;
  onCreateRoom: () => void;
}

export const RoomList: React.FC<RoomListProps> = ({
  user,
  onRoomSelect,
  onCreateRoom,
}) => {
  const [rooms, setRooms] = useState<Room[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchRooms();
  }, []);

  const fetchRooms = async () => {
    try {
      const response = await fetch('/api/rooms');
      const data = await response.json();
      if (data.rooms) {
        setRooms(data.rooms);
      }
    } catch (error) {
      console.error('Error fetching rooms:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Available Rooms
          </h3>
          <button
            onClick={onCreateRoom}
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <Plus className="h-4 w-4 mr-1" />
            Create Room
          </button>
        </div>

        {rooms.length === 0 ? (
          <div className="text-center py-8">
            <Users className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No rooms available</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by creating a new room.
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {rooms.map((room) => (
              <div
                key={room.id}
                onClick={() => onRoomSelect(room)}
                className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center">
                      <h4 className="text-sm font-medium text-gray-900">
                        {room.name}
                      </h4>
                      {room.is_private && (
                        <Lock className="ml-2 h-4 w-4 text-gray-400" />
                      )}
                    </div>
                    {room.description && (
                      <p className="mt-1 text-sm text-gray-500">
                        {room.description}
                      </p>
                    )}
                    <div className="mt-2 flex items-center text-xs text-gray-500">
                      <Users className="h-3 w-3 mr-1" />
                      {room.participants?.length || 0} / {room.max_participants} participants
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
