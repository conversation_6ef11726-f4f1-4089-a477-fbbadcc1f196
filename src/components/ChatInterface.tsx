'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Send, Video, VideoOff, Mic, MicOff, Phone, PhoneOff } from 'lucide-react';
import { useSocket } from '../contexts/SocketContext';
import { useWebRTC } from '../hooks/useWebRTC';
import { useChat } from '../hooks/useChat';
import { TypingIndicator } from './TypingIndicator';
import { LoadingSpinner } from './LoadingSpinner';
import type { Room, User, Message } from '../types';

interface ChatInterfaceProps {
  room: Room;
  user: User;
  onLeaveRoom: () => void;
}

export const ChatInterface: React.FC<ChatInterfaceProps> = ({
  room,
  user,
  onLeaveRoom,
}) => {
  const { socket } = useSocket();
  const [newMessage, setNewMessage] = useState('');
  const [participantCount, setParticipantCount] = useState(room.participants?.length || 0);
  const [incomingCall, setIncomingCall] = useState<{ callId: string; initiatorId: string } | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const {
    localStream,
    remoteStreams,
    isCallActive,
    isMuted,
    isVideoOff,
    callError,
    isConnecting,
    localVideoRef,
    startCall,
    endCall,
    toggleMute,
    toggleVideo,
  } = useWebRTC({ userId: user.id, roomId: room.id });

  const {
    messages,
    typingUsers,
    loading: chatLoading,
    error: chatError,
    sendMessage,
    startTyping,
    stopTyping,
  } = useChat({ room, user });

  useEffect(() => {
    if (!socket) return;

    // Identify user first
    socket.emit('identify-user', user.id);

    // Join the room
    socket.emit('join-room', room.id, user.id);

    // Listen for participant updates
    const handleParticipantUpdate = (data: { roomId: string; participantCount: number }) => {
      if (data.roomId === room.id) {
        setParticipantCount(data.participantCount);
      }
    };

    // Listen for incoming video calls
    const handleIncomingCall = (callId: string, initiatorId: string, roomId: string) => {
      console.log(`Incoming call from ${initiatorId} in room ${roomId}`);
      if (roomId === room.id && initiatorId !== user.id) {
        setIncomingCall({ callId, initiatorId });
      }
    };

    socket.on('room-participants-updated', handleParticipantUpdate);
    socket.on('video-call-initiated', handleIncomingCall);

    return () => {
      socket.emit('leave-room', room.id, user.id);
      socket.off('room-participants-updated', handleParticipantUpdate);
      socket.off('video-call-initiated', handleIncomingCall);
    };
  }, [socket, room.id, user.id]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim()) return;

    await sendMessage(newMessage);
    setNewMessage('');
    stopTyping();
  };

  const handleTyping = (value: string) => {
    setNewMessage(value);

    if (value.trim()) {
      startTyping();
    } else {
      stopTyping();
    }
  };

  const handleJoinCall = async () => {
    if (incomingCall) {
      await startCall();
      // Notify the server that we joined the call
      socket?.emit('join-video-call', incomingCall.callId, user.id);
      setIncomingCall(null);
    }
  };

  const handleDeclineCall = () => {
    setIncomingCall(null);
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="flex flex-col h-screen bg-white">
      {/* Header */}
      <div className="bg-indigo-600 text-white px-4 py-3 flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold">{room.name}</h2>
          <p className="text-indigo-200 text-sm">
            {participantCount} participant{participantCount !== 1 ? 's' : ''} online
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {/* Video call controls */}
          {isCallActive ? (
            <>
              <button
                onClick={toggleMute}
                className={`p-2 rounded-full ${
                  isMuted ? 'bg-red-600' : 'bg-indigo-500'
                } hover:bg-opacity-80 transition-colors`}
                title={isMuted ? 'Unmute' : 'Mute'}
              >
                {isMuted ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
              </button>
              <button
                onClick={toggleVideo}
                className={`p-2 rounded-full ${
                  isVideoOff ? 'bg-red-600' : 'bg-indigo-500'
                } hover:bg-opacity-80 transition-colors`}
                title={isVideoOff ? 'Turn on camera' : 'Turn off camera'}
              >
                {isVideoOff ? <VideoOff className="h-4 w-4" /> : <Video className="h-4 w-4" />}
              </button>
              <button
                onClick={endCall}
                className="p-2 rounded-full bg-red-600 hover:bg-red-700 transition-colors"
                title="End call"
              >
                <PhoneOff className="h-4 w-4" />
              </button>
            </>
          ) : (
            <button
              onClick={startCall}
              disabled={isConnecting}
              className="p-2 rounded-full bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              title="Start video call"
            >
              {isConnecting ? (
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
              ) : (
                <Phone className="h-4 w-4" />
              )}
            </button>
          )}
          <button
            onClick={onLeaveRoom}
            className="px-3 py-1 bg-indigo-500 hover:bg-indigo-400 rounded text-sm transition-colors"
          >
            Leave Room
          </button>
        </div>
      </div>

      {/* Video call area */}
      {isCallActive && (
        <div className="bg-gradient-to-r from-gray-900 to-gray-800 p-4 animate-slideIn">
          <div className="flex space-x-4 overflow-x-auto custom-scrollbar pb-2">
            {/* Local video */}
            <div className="flex-shrink-0 relative group">
              <video
                ref={localVideoRef}
                autoPlay
                muted
                playsInline
                controls={false}
                className="w-48 h-36 bg-gray-800 rounded-lg object-cover shadow-lg border-2 border-indigo-500 transition-transform duration-200 group-hover:scale-105"
                style={{ transform: 'scaleX(-1)' }} // Mirror the video like a selfie
                onLoadedMetadata={() => {
                  console.log('Local video metadata loaded');
                  if (localVideoRef.current) {
                    localVideoRef.current.play().catch(console.error);
                  }
                }}
                onCanPlay={() => {
                  console.log('Local video can play');
                }}
                onError={(e) => {
                  console.error('Local video error:', e);
                }}
              />
              <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                You {isMuted && '🔇'} {isVideoOff && '📹'}
              </div>
              {isVideoOff && (
                <div className="absolute inset-0 bg-gray-800 rounded-lg flex items-center justify-center">
                  <div className="text-white text-center">
                    <VideoOff className="h-8 w-8 mx-auto mb-2" />
                    <p className="text-sm">Camera off</p>
                  </div>
                </div>
              )}
            </div>

            {/* Remote videos */}
            {Array.from(remoteStreams.entries()).map(([userId, stream]) => (
              <div key={userId} className="flex-shrink-0 relative group animate-fadeIn">
                <video
                  autoPlay
                  playsInline
                  className="w-48 h-36 bg-gray-800 rounded-lg object-cover shadow-lg border-2 border-green-500 transition-transform duration-200 group-hover:scale-105"
                  ref={(video) => {
                    if (video && video.srcObject !== stream) {
                      console.log(`Setting remote video source for user ${userId}:`, stream);
                      video.srcObject = stream;
                      video.play().catch(console.error);
                    }
                  }}
                  onLoadedMetadata={() => {
                    console.log(`Remote video metadata loaded for user ${userId}`);
                  }}
                />
                <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                  User {userId.slice(-4)}
                </div>
              </div>
            ))}

            {/* Connecting indicator */}
            {isConnecting && (
              <div className="flex-shrink-0 w-48 h-36 bg-gray-800 rounded-lg flex items-center justify-center animate-pulse">
                <div className="text-white text-center">
                  <LoadingSpinner size="md" color="white" className="mx-auto mb-2" />
                  <p className="text-sm">Connecting...</p>
                </div>
              </div>
            )}

            {/* Debug info */}
            {isCallActive && (
              <div className="flex-shrink-0 w-48 h-36 bg-gray-700 rounded-lg p-2 text-white text-xs">
                <div>Call Active: {isCallActive ? 'Yes' : 'No'}</div>
                <div>Local Stream: {localStream ? 'Yes' : 'No'}</div>
                <div>Remote Streams: {remoteStreams.size}</div>
                <div>Video Tracks: {localStream?.getVideoTracks().length || 0}</div>
                <div>Audio Tracks: {localStream?.getAudioTracks().length || 0}</div>
                <div>Video Enabled: {localStream?.getVideoTracks()[0]?.enabled ? 'Yes' : 'No'}</div>
                <button
                  onClick={() => {
                    if (localVideoRef.current && localStream) {
                      console.log('Manual video setup');
                      localVideoRef.current.srcObject = localStream;
                      localVideoRef.current.play();
                    }
                  }}
                  className="mt-1 bg-blue-600 px-2 py-1 rounded text-xs"
                >
                  Fix Video
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Incoming call notification */}
      {incomingCall && !isCallActive && (
        <div className="bg-green-50 border border-green-200 p-4 mx-4 animate-fadeIn">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Phone className="h-5 w-5 text-green-600 mr-2" />
              <div>
                <h3 className="text-sm font-medium text-green-800">
                  Incoming Video Call
                </h3>
                <p className="text-sm text-green-700">
                  User {incomingCall.initiatorId.slice(-4)} is calling...
                </p>
              </div>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={handleJoinCall}
                className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors"
              >
                Join
              </button>
              <button
                onClick={handleDeclineCall}
                className="bg-gray-600 text-white px-3 py-1 rounded text-sm hover:bg-gray-700 transition-colors"
              >
                Decline
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Error messages */}
      {(chatError || callError) && (
        <div className="bg-red-50 border border-red-200 p-3 mx-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                {callError ? 'Video Call Error' : 'Chat Error'}
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{callError || chatError}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4 custom-scrollbar">
        {messages.map((message, index) => (
          <div
            key={message.id}
            className={`flex ${
              message.user_id === user.id ? 'justify-end' : 'justify-start'
            } message-bubble`}
            style={{ animationDelay: `${index * 0.05}s` }}
          >
            <div
              className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg shadow-sm transition-all duration-200 hover:shadow-md ${
                message.user_id === user.id
                  ? 'bg-indigo-600 text-white rounded-br-sm'
                  : 'bg-white text-gray-900 border border-gray-200 rounded-bl-sm'
              }`}
            >
              {message.user_id !== user.id && (
                <p className="text-xs font-medium mb-1 text-indigo-600">
                  {message.user?.username || 'Unknown User'}
                </p>
              )}
              <p className="text-sm leading-relaxed">{message.content}</p>
              <p
                className={`text-xs mt-1 ${
                  message.user_id === user.id ? 'text-indigo-200' : 'text-gray-500'
                }`}
              >
                {formatTime(message.created_at)}
              </p>
            </div>
          </div>
        ))}

        {/* Typing indicator */}
        <TypingIndicator userCount={typingUsers.length} />
        
        <div ref={messagesEndRef} />
      </div>

      {/* Message input */}
      <form onSubmit={handleSendMessage} className="border-t bg-white p-4">
        <div className="flex space-x-3">
          <input
            type="text"
            value={newMessage}
            onChange={(e) => handleTyping(e.target.value)}
            placeholder="Type a message..."
            className="flex-1 border border-gray-300 rounded-full px-4 py-3 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white text-gray-900 placeholder-gray-500"
            maxLength={1000}
          />
          <button
            type="submit"
            disabled={!newMessage.trim()}
            className="bg-indigo-600 text-white p-3 rounded-full hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 active:scale-95"
          >
            <Send className="h-4 w-4" />
          </button>
        </div>
      </form>
    </div>
  );
};
