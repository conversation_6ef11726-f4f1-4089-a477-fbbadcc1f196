'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Send, Video, VideoOff, Mic, MicOff, Phone, PhoneOff } from 'lucide-react';
import { useSocket } from '../contexts/SocketContext';
import { useWebRTC } from '../hooks/useWebRTC';
import type { Room, User, Message } from '../types';

interface ChatInterfaceProps {
  room: Room;
  user: User;
  onLeaveRoom: () => void;
}

export const ChatInterface: React.FC<ChatInterfaceProps> = ({
  room,
  user,
  onLeaveRoom,
}) => {
  const { socket } = useSocket();
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [typingUsers, setTypingUsers] = useState<string[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  const {
    localStream,
    remoteStreams,
    isCallActive,
    isMuted,
    isVideoOff,
    localVideoRef,
    startCall,
    endCall,
    toggleMute,
    toggleVideo,
  } = useWebRTC({ userId: user.id, roomId: room.id });

  useEffect(() => {
    if (socket) {
      // Join the room
      socket.emit('join-room', room.id, user.id);

      // Listen for new messages
      socket.on('new-message', (message: Message) => {
        setMessages(prev => [...prev, message]);
      });

      // Listen for typing indicators
      socket.on('user-typing', (userId: string) => {
        setTypingUsers(prev => [...prev.filter(id => id !== userId), userId]);
      });

      socket.on('user-stopped-typing', (userId: string) => {
        setTypingUsers(prev => prev.filter(id => id !== userId));
      });

      return () => {
        socket.emit('leave-room', room.id, user.id);
        socket.off('new-message');
        socket.off('user-typing');
        socket.off('user-stopped-typing');
      };
    }
  }, [socket, room.id, user.id]);

  useEffect(() => {
    // Fetch existing messages
    fetchMessages();
  }, [room.id]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const fetchMessages = async () => {
    try {
      const response = await fetch(`/api/messages?roomId=${room.id}&limit=50`);
      const data = await response.json();
      if (data.messages) {
        setMessages(data.messages);
      }
    } catch (error) {
      console.error('Error fetching messages:', error);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || !socket) return;

    const messageData = {
      room_id: room.id,
      user_id: user.id,
      content: newMessage.trim(),
      message_type: 'text' as const,
    };

    socket.emit('send-message', messageData);
    setNewMessage('');

    // Stop typing indicator
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    socket.emit('typing-stop', user.id, room.id);
  };

  const handleTyping = (value: string) => {
    setNewMessage(value);

    if (!socket) return;

    // Start typing indicator
    socket.emit('typing-start', user.id, room.id);

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Stop typing after 2 seconds of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      socket.emit('typing-stop', user.id, room.id);
    }, 2000);
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="flex flex-col h-screen bg-white">
      {/* Header */}
      <div className="bg-indigo-600 text-white px-4 py-3 flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold">{room.name}</h2>
          <p className="text-indigo-200 text-sm">
            {room.participants?.length || 0} participants
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {/* Video call controls */}
          {isCallActive ? (
            <>
              <button
                onClick={toggleMute}
                className={`p-2 rounded-full ${
                  isMuted ? 'bg-red-600' : 'bg-indigo-500'
                } hover:bg-opacity-80`}
              >
                {isMuted ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
              </button>
              <button
                onClick={toggleVideo}
                className={`p-2 rounded-full ${
                  isVideoOff ? 'bg-red-600' : 'bg-indigo-500'
                } hover:bg-opacity-80`}
              >
                {isVideoOff ? <VideoOff className="h-4 w-4" /> : <Video className="h-4 w-4" />}
              </button>
              <button
                onClick={endCall}
                className="p-2 rounded-full bg-red-600 hover:bg-red-700"
              >
                <PhoneOff className="h-4 w-4" />
              </button>
            </>
          ) : (
            <button
              onClick={startCall}
              className="p-2 rounded-full bg-green-600 hover:bg-green-700"
            >
              <Phone className="h-4 w-4" />
            </button>
          )}
          <button
            onClick={onLeaveRoom}
            className="px-3 py-1 bg-indigo-500 hover:bg-indigo-400 rounded text-sm"
          >
            Leave Room
          </button>
        </div>
      </div>

      {/* Video call area */}
      {isCallActive && (
        <div className="bg-gray-900 p-4 flex space-x-4 overflow-x-auto">
          {/* Local video */}
          <div className="flex-shrink-0">
            <video
              ref={localVideoRef}
              autoPlay
              muted
              playsInline
              className="w-48 h-36 bg-gray-800 rounded object-cover"
            />
            <p className="text-white text-xs text-center mt-1">You</p>
          </div>
          
          {/* Remote videos */}
          {Array.from(remoteStreams.entries()).map(([userId, stream]) => (
            <div key={userId} className="flex-shrink-0">
              <video
                autoPlay
                playsInline
                className="w-48 h-36 bg-gray-800 rounded object-cover"
                ref={(video) => {
                  if (video) video.srcObject = stream;
                }}
              />
              <p className="text-white text-xs text-center mt-1">User {userId}</p>
            </div>
          ))}
        </div>
      )}

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${
              message.user_id === user.id ? 'justify-end' : 'justify-start'
            }`}
          >
            <div
              className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                message.user_id === user.id
                  ? 'bg-indigo-600 text-white'
                  : 'bg-gray-200 text-gray-900'
              }`}
            >
              {message.user_id !== user.id && (
                <p className="text-xs font-medium mb-1">
                  {message.user?.username || 'Unknown User'}
                </p>
              )}
              <p className="text-sm">{message.content}</p>
              <p
                className={`text-xs mt-1 ${
                  message.user_id === user.id ? 'text-indigo-200' : 'text-gray-500'
                }`}
              >
                {formatTime(message.created_at)}
              </p>
            </div>
          </div>
        ))}
        
        {/* Typing indicator */}
        {typingUsers.length > 0 && (
          <div className="flex justify-start">
            <div className="bg-gray-200 text-gray-900 px-4 py-2 rounded-lg">
              <p className="text-sm">
                {typingUsers.length === 1
                  ? 'Someone is typing...'
                  : `${typingUsers.length} people are typing...`}
              </p>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Message input */}
      <form onSubmit={handleSendMessage} className="border-t p-4">
        <div className="flex space-x-2">
          <input
            type="text"
            value={newMessage}
            onChange={(e) => handleTyping(e.target.value)}
            placeholder="Type a message..."
            className="flex-1 border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
          />
          <button
            type="submit"
            disabled={!newMessage.trim()}
            className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Send className="h-4 w-4" />
          </button>
        </div>
      </form>
    </div>
  );
};
