-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create users table (extends auth.users)
CREATE TABLE public.users (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  username TEXT UNIQUE NOT NULL,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create rooms table
CREATE TABLE public.rooms (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  host_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  is_private BOOLEAN DEFAULT FALSE,
  max_participants INTEGER DEFAULT 10,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create room_participants table
CREATE TABLE public.room_participants (
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  room_id UUID REFERENCES public.rooms(id) ON DELETE CASCADE,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_host BOOLEAN DEFAULT FALSE,
  PRIMARY KEY (user_id, room_id)
);

-- Create messages table
CREATE TABLE public.messages (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  room_id UUID REFERENCES public.rooms(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  content TEXT NOT NULL,
  message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'system', 'video_call_start', 'video_call_end')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create video_calls table
CREATE TABLE public.video_calls (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  room_id UUID REFERENCES public.rooms(id) ON DELETE CASCADE NOT NULL,
  initiated_by UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  participants UUID[] DEFAULT '{}',
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'ended')),
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  ended_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for better performance
CREATE INDEX idx_messages_room_id ON public.messages(room_id);
CREATE INDEX idx_messages_created_at ON public.messages(created_at);
CREATE INDEX idx_room_participants_room_id ON public.room_participants(room_id);
CREATE INDEX idx_video_calls_room_id ON public.video_calls(room_id);
CREATE INDEX idx_video_calls_status ON public.video_calls(status);

-- Enable Row Level Security (RLS)
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.rooms ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.room_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.video_calls ENABLE ROW LEVEL SECURITY;

-- Create RLS policies

-- Users policies
CREATE POLICY "Users can view all users" ON public.users
  FOR SELECT USING (true);

CREATE POLICY "Users can update their own profile" ON public.users
  FOR UPDATE USING (auth.uid() = id);

-- Rooms policies
CREATE POLICY "Anyone can view public rooms" ON public.rooms
  FOR SELECT USING (NOT is_private OR auth.uid() IN (
    SELECT user_id FROM public.room_participants WHERE room_id = id
  ));

CREATE POLICY "Authenticated users can create rooms" ON public.rooms
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Room hosts can update their rooms" ON public.rooms
  FOR UPDATE USING (auth.uid() = host_id);

CREATE POLICY "Room hosts can delete their rooms" ON public.rooms
  FOR DELETE USING (auth.uid() = host_id);

-- Room participants policies
CREATE POLICY "Room participants can view room participants" ON public.room_participants
  FOR SELECT USING (auth.uid() IN (
    SELECT user_id FROM public.room_participants WHERE room_id = room_participants.room_id
  ));

CREATE POLICY "Users can join rooms" ON public.room_participants
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can leave rooms" ON public.room_participants
  FOR DELETE USING (auth.uid() = user_id);

-- Messages policies
CREATE POLICY "Room participants can view messages" ON public.messages
  FOR SELECT USING (auth.uid() IN (
    SELECT user_id FROM public.room_participants WHERE room_id = messages.room_id
  ));

CREATE POLICY "Room participants can send messages" ON public.messages
  FOR INSERT WITH CHECK (
    auth.uid() = user_id AND 
    auth.uid() IN (
      SELECT user_id FROM public.room_participants WHERE room_id = messages.room_id
    )
  );

-- Video calls policies
CREATE POLICY "Room participants can view video calls" ON public.video_calls
  FOR SELECT USING (auth.uid() IN (
    SELECT user_id FROM public.room_participants WHERE room_id = video_calls.room_id
  ));

CREATE POLICY "Room participants can create video calls" ON public.video_calls
  FOR INSERT WITH CHECK (
    auth.uid() = initiated_by AND 
    auth.uid() IN (
      SELECT user_id FROM public.room_participants WHERE room_id = video_calls.room_id
    )
  );

CREATE POLICY "Video call participants can update calls" ON public.video_calls
  FOR UPDATE USING (auth.uid() = ANY(participants) OR auth.uid() = initiated_by);

-- Create function to automatically create user profile
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, username)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1))
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user creation
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create function to clean up room participants when user leaves
CREATE OR REPLACE FUNCTION public.cleanup_empty_rooms()
RETURNS TRIGGER AS $$
BEGIN
  -- Delete rooms that have no participants left
  DELETE FROM public.rooms 
  WHERE id = OLD.room_id 
  AND NOT EXISTS (
    SELECT 1 FROM public.room_participants 
    WHERE room_id = OLD.room_id
  );
  
  RETURN OLD;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for room cleanup
CREATE TRIGGER on_participant_removed
  AFTER DELETE ON public.room_participants
  FOR EACH ROW EXECUTE FUNCTION public.cleanup_empty_rooms();
