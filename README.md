# Realtime Chat & Video App

A modern realtime chat application with video calling capabilities built with Next.js, Socket.io, and WebRTC. This version uses mock authentication for easy testing and development.

## Features

- 🔐 **Mock Authentication** - Simple test authentication system for development
- 💬 **Realtime Chat** - Instant messaging with typing indicators
- 📹 **Video Calling** - WebRTC-based peer-to-peer video calls
- 🏠 **Room Management** - Create, join, and manage chat rooms
- 📱 **Responsive Design** - Works seamlessly on desktop and mobile
- 🎨 **Modern UI** - Beautiful interface with smooth animations
- ⚡ **Real-time Updates** - Live participant updates and message delivery

## Tech Stack

- **Frontend**: Next.js 15, React 19, <PERSON>Script, Tailwind CSS
- **Backend**: Socket.io, Node.js
- **Data Storage**: In-memory mock data (for testing)
- **Authentication**: Mock authentication system
- **Video Calling**: WebRTC
- **Real-time Communication**: Socket.io
- **Testing**: Jest, React Testing Library

## Prerequisites

- Node.js 18+
- npm or yarn

## Setup Instructions

### 1. Clone the repository

```bash
git clone <repository-url>
cd next-ocr
```

### 2. Install dependencies

```bash
npm install
```

### 3. Configure environment variables (optional)

The `.env.local` file is already configured for testing:

```env
SOCKET_IO_PORT=3001
MOCK_AUTH_ENABLED=true
```

### 4. Run the application

For development with both Next.js and Socket.io servers:

```bash
npm run dev:full
```

Or run them separately:

```bash
# Terminal 1 - Next.js app
npm run dev

# Terminal 2 - Socket.io server
npm run socket
```

### 5. Access the application

Open [http://localhost:3000](http://localhost:3000) in your browser.

### 6. Test the application

1. **Sign up/Sign in**: Use any email and password - the mock auth system will create test users
2. **Create rooms**: Click "Create Room" to make new chat rooms
3. **Join rooms**: Click on any room to join and start chatting
4. **Video calls**: Click the phone icon to start a video call with other participants
5. **Chat**: Send messages and see real-time typing indicators

## Available Scripts

- `npm run dev` - Start Next.js development server
- `npm run socket` - Start Socket.io server
- `npm run dev:full` - Start both servers concurrently
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run test` - Run tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage report
- `npm run lint` - Run ESLint

## Project Structure

```
src/
├── app/                 # Next.js app directory
├── components/          # React components
├── contexts/           # React contexts (Socket.io)
├── hooks/              # Custom React hooks
├── lib/                # Utility libraries (Supabase, auth)
├── server/             # Socket.io server code
└── types/              # TypeScript type definitions
```

## Key Components

- **AuthForm** - User authentication interface
- **RoomList** - Display and manage chat rooms
- **ChatInterface** - Main chat and video calling interface
- **CreateRoomModal** - Room creation dialog
- **TypingIndicator** - Shows when users are typing
- **LoadingSpinner** - Reusable loading component

## Custom Hooks

- **useRoomManagement** - Handle room operations
- **useChat** - Manage chat functionality
- **useWebRTC** - Handle video calling with WebRTC

## Mock Data Structure

The application uses in-memory mock data for testing:
- **Users** - Stored in localStorage and memory
- **Rooms** - Pre-populated with sample rooms
- **Messages** - Stored per room in memory
- **Participants** - Tracked in real-time via Socket.io

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run tests and ensure they pass
6. Submit a pull request

## License

This project is licensed under the MIT License.
