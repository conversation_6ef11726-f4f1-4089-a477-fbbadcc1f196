const { createServer } = require('http');
const { Server } = require('socket.io');

// Mock data storage for the Socket.io server
const rooms = new Map(); // roomId -> Set of userIds
const messages = new Map(); // roomId -> Array of messages
const userSockets = new Map(); // userId -> socketId
const socketUsers = new Map(); // socketId -> userId
const activeCalls = new Map(); // roomId -> Set of userIds in call

const httpServer = createServer();
const io = new Server(httpServer, {
  cors: {
    origin: process.env.NODE_ENV === 'production' ? false : ['http://localhost:3000', 'http://localhost:3002'],
    methods: ['GET', 'POST'],
    credentials: true
  },
});

io.on('connection', (socket) => {
  console.log('Socket connected:', socket.id);

  // Handle user identification
  socket.on('identify-user', (userId) => {
    userSockets.set(userId, socket.id);
    socketUsers.set(socket.id, userId);
    console.log(`User ${userId} identified with socket ${socket.id}`);
  });

  // Room management
  socket.on('join-room', (roomId, userId) => {
    try {
      socket.join(roomId);

      // Add user to room participants in memory
      if (!rooms.has(roomId)) {
        rooms.set(roomId, new Set());
      }
      rooms.get(roomId).add(userId);

      // Get current participants
      const participants = Array.from(rooms.get(roomId) || []);

      // Mock room data
      const room = {
        id: roomId,
        name: `Room ${roomId}`,
        participants: participants.map(id => ({
          user_id: id,
          user: {
            id: id,
            username: `User ${id.slice(-4)}`,
            email: `${id}@example.com`
          }
        }))
      };

      socket.emit('room-joined', room);
      socket.to(roomId).emit('user-joined-room', userId, roomId);

      // Broadcast updated participant count to all users in room
      io.to(roomId).emit('room-participants-updated', {
        roomId,
        participantCount: participants.length,
        participants: participants
      });

      console.log(`User ${userId} joined room ${roomId}. Total participants: ${participants.length}`);
    } catch (error) {
      console.error('Error joining room:', error);
    }
  });

  socket.on('leave-room', (roomId, userId) => {
    try {
      socket.leave(roomId);

      // Remove user from room participants in memory
      if (rooms.has(roomId)) {
        rooms.get(roomId).delete(userId);

        const remainingParticipants = Array.from(rooms.get(roomId) || []);

        if (remainingParticipants.length === 0) {
          rooms.delete(roomId);
        } else {
          // Broadcast updated participant count to remaining users
          io.to(roomId).emit('room-participants-updated', {
            roomId,
            participantCount: remainingParticipants.length,
            participants: remainingParticipants
          });
        }
      }

      socket.emit('room-left', roomId);
      socket.to(roomId).emit('user-left-room', userId, roomId);

      console.log(`User ${userId} left room ${roomId}`);
    } catch (error) {
      console.error('Error leaving room:', error);
    }
  });

  // Chat messaging
  socket.on('send-message', (messageData) => {
    try {
      // Create mock message with timestamp and ID
      const message = {
        ...messageData,
        id: Math.random().toString(36).substr(2, 9),
        created_at: new Date().toISOString(),
        user: {
          id: messageData.user_id,
          username: `User ${messageData.user_id.slice(-4)}`,
          email: '<EMAIL>'
        }
      };

      // Store message in memory
      if (!messages.has(messageData.room_id)) {
        messages.set(messageData.room_id, []);
      }
      messages.get(messageData.room_id).push(message);

      io.to(messageData.room_id).emit('new-message', message);
      console.log(`Message sent in room ${messageData.room_id}:`, message.content);
    } catch (error) {
      console.error('Error sending message:', error);
    }
  });

  // Typing indicators
  socket.on('typing-start', (userId, roomId) => {
    socket.to(roomId).emit('user-typing', userId, roomId);
  });

  socket.on('typing-stop', (userId, roomId) => {
    socket.to(roomId).emit('user-stopped-typing', userId, roomId);
  });

  // Video call management
  socket.on('initiate-video-call', (roomId, userId) => {
    try {
      const callId = Math.random().toString(36).substr(2, 9);

      // Add user to active call
      if (!activeCalls.has(roomId)) {
        activeCalls.set(roomId, new Set());
      }
      activeCalls.get(roomId).add(userId);

      io.to(roomId).emit('video-call-initiated', callId, userId, roomId);
      console.log(`Video call initiated in room ${roomId} by user ${userId}`);
    } catch (error) {
      console.error('Error initiating video call:', error);
    }
  });

  socket.on('join-video-call', (callId, userId) => {
    try {
      // Find the room this user is in
      const roomId = Array.from(socket.rooms).find(room => room !== socket.id);
      if (roomId) {
        // Add user to active call
        if (!activeCalls.has(roomId)) {
          activeCalls.set(roomId, new Set());
        }
        activeCalls.get(roomId).add(userId);

        // Get existing call participants
        const existingParticipants = Array.from(activeCalls.get(roomId) || []);

        // Emit to all users in the room that this user joined the call
        io.to(roomId).emit('video-call-joined', callId, userId);

        // Send list of existing participants to the new joiner
        const newJoinerSocketId = userSockets.get(userId);
        if (newJoinerSocketId) {
          io.to(newJoinerSocketId).emit('existing-call-participants', existingParticipants.filter(id => id !== userId));
        }

        console.log(`User ${userId} joined video call ${callId} in room ${roomId}. Existing participants:`, existingParticipants);
      }
    } catch (error) {
      console.error('Error joining video call:', error);
    }
  });

  socket.on('leave-video-call', (callId, userId) => {
    try {
      const roomId = Array.from(socket.rooms).find(room => room !== socket.id);
      if (roomId && activeCalls.has(roomId)) {
        activeCalls.get(roomId).delete(userId);

        // If no one left in call, remove the call
        if (activeCalls.get(roomId).size === 0) {
          activeCalls.delete(roomId);
        }

        io.to(roomId).emit('video-call-left', callId, userId);
        console.log(`User ${userId} left video call ${callId}`);
      }
    } catch (error) {
      console.error('Error leaving video call:', error);
    }
  });

  // WebRTC signaling
  socket.on('webrtc-offer', (offer, targetUserId, fromUserId) => {
    console.log(`WebRTC offer from ${fromUserId} to ${targetUserId}`);
    // Find the socket ID for the target user
    const targetSocketId = userSockets.get(targetUserId);
    if (targetSocketId) {
      io.to(targetSocketId).emit('webrtc-offer', offer, fromUserId);
      console.log(`Sent offer to socket ${targetSocketId}`);
    } else {
      console.log(`Target user ${targetUserId} not found`);
    }
  });

  socket.on('webrtc-answer', (answer, targetUserId, fromUserId) => {
    console.log(`WebRTC answer from ${fromUserId} to ${targetUserId}`);
    const targetSocketId = userSockets.get(targetUserId);
    if (targetSocketId) {
      io.to(targetSocketId).emit('webrtc-answer', answer, fromUserId);
      console.log(`Sent answer to socket ${targetSocketId}`);
    } else {
      console.log(`Target user ${targetUserId} not found`);
    }
  });

  socket.on('webrtc-ice-candidate', (candidate, targetUserId, fromUserId) => {
    console.log(`WebRTC ICE candidate from ${fromUserId} to ${targetUserId}`);
    const targetSocketId = userSockets.get(targetUserId);
    if (targetSocketId) {
      io.to(targetSocketId).emit('webrtc-ice-candidate', candidate, fromUserId);
    } else {
      console.log(`Target user ${targetUserId} not found`);
    }
  });

  socket.on('disconnect', () => {
    const userId = socketUsers.get(socket.id);

    if (userId) {
      // Remove user from all rooms
      for (const [roomId, participants] of rooms.entries()) {
        if (participants.has(userId)) {
          participants.delete(userId);

          const remainingParticipants = Array.from(participants);

          if (remainingParticipants.length === 0) {
            rooms.delete(roomId);
          } else {
            // Notify remaining participants
            io.to(roomId).emit('user-left-room', userId, roomId);
            io.to(roomId).emit('room-participants-updated', {
              roomId,
              participantCount: remainingParticipants.length,
              participants: remainingParticipants
            });
          }
        }
      }

      // Clean up user mappings
      userSockets.delete(userId);
      socketUsers.delete(socket.id);

      console.log(`User ${userId} disconnected (socket ${socket.id})`);
    } else {
      console.log('Socket disconnected:', socket.id);
    }
  });
});

const PORT = process.env.SOCKET_IO_PORT || 3001;
httpServer.listen(PORT, () => {
  console.log(`Socket.io server running on port ${PORT}`);
});
