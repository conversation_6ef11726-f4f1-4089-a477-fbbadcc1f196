const { createServer } = require('http');
const { Server } = require('socket.io');

// Mock data storage for the Socket.io server
const rooms = new Map();
const messages = new Map();

const httpServer = createServer();
const io = new Server(httpServer, {
  cors: {
    origin: process.env.NODE_ENV === 'production' ? false : ['http://localhost:3000'],
    methods: ['GET', 'POST'],
  },
});

io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  // Room management
  socket.on('join-room', (roomId, userId) => {
    try {
      socket.join(roomId);

      // Add user to room participants in memory
      if (!rooms.has(roomId)) {
        rooms.set(roomId, new Set());
      }
      rooms.get(roomId).add(userId);

      // Mock room data
      const room = {
        id: roomId,
        name: `Room ${roomId}`,
        participants: Array.from(rooms.get(roomId) || [])
      };

      socket.emit('room-joined', room);
      socket.to(roomId).emit('user-joined-room', userId, roomId);

      console.log(`User ${userId} joined room ${roomId}`);
    } catch (error) {
      console.error('Error joining room:', error);
    }
  });

  socket.on('leave-room', (roomId, userId) => {
    try {
      socket.leave(roomId);

      // Remove user from room participants in memory
      if (rooms.has(roomId)) {
        rooms.get(roomId).delete(userId);
        if (rooms.get(roomId).size === 0) {
          rooms.delete(roomId);
        }
      }

      socket.emit('room-left', roomId);
      socket.to(roomId).emit('user-left-room', userId, roomId);

      console.log(`User ${userId} left room ${roomId}`);
    } catch (error) {
      console.error('Error leaving room:', error);
    }
  });

  // Chat messaging
  socket.on('send-message', (messageData) => {
    try {
      // Create mock message with timestamp and ID
      const message = {
        ...messageData,
        id: Math.random().toString(36).substr(2, 9),
        created_at: new Date().toISOString(),
        user: {
          id: messageData.user_id,
          username: `User ${messageData.user_id.slice(-4)}`,
          email: '<EMAIL>'
        }
      };

      // Store message in memory
      if (!messages.has(messageData.room_id)) {
        messages.set(messageData.room_id, []);
      }
      messages.get(messageData.room_id).push(message);

      io.to(messageData.room_id).emit('new-message', message);
      console.log(`Message sent in room ${messageData.room_id}:`, message.content);
    } catch (error) {
      console.error('Error sending message:', error);
    }
  });

  // Typing indicators
  socket.on('typing-start', (userId, roomId) => {
    socket.to(roomId).emit('user-typing', userId, roomId);
  });

  socket.on('typing-stop', (userId, roomId) => {
    socket.to(roomId).emit('user-stopped-typing', userId, roomId);
  });

  // Video call management
  socket.on('initiate-video-call', (roomId, userId) => {
    try {
      const callId = Math.random().toString(36).substr(2, 9);

      io.to(roomId).emit('video-call-initiated', callId, userId, roomId);
      console.log(`Video call initiated in room ${roomId} by user ${userId}`);
    } catch (error) {
      console.error('Error initiating video call:', error);
    }
  });

  socket.on('join-video-call', (callId, userId) => {
    try {
      // For simplicity, we'll just emit the event
      // In a real app, you'd track call participants
      socket.emit('video-call-joined', callId, userId);
      console.log(`User ${userId} joined video call ${callId}`);
    } catch (error) {
      console.error('Error joining video call:', error);
    }
  });

  socket.on('leave-video-call', (callId, userId) => {
    try {
      // For simplicity, we'll just emit the event
      socket.emit('video-call-left', callId, userId);
      console.log(`User ${userId} left video call ${callId}`);
    } catch (error) {
      console.error('Error leaving video call:', error);
    }
  });

  // WebRTC signaling
  socket.on('webrtc-offer', (offer, targetUserId, fromUserId) => {
    socket.to(targetUserId).emit('webrtc-offer', offer, fromUserId);
  });

  socket.on('webrtc-answer', (answer, targetUserId, fromUserId) => {
    socket.to(targetUserId).emit('webrtc-answer', answer, fromUserId);
  });

  socket.on('webrtc-ice-candidate', (candidate, targetUserId, fromUserId) => {
    socket.to(targetUserId).emit('webrtc-ice-candidate', candidate, fromUserId);
  });

  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
  });
});

const PORT = process.env.SOCKET_IO_PORT || 3001;
httpServer.listen(PORT, () => {
  console.log(`Socket.io server running on port ${PORT}`);
});
